<template>
    <div class="w-100">
        <loading :active.sync="isLoading" :can-cancel="false" :is-full-page="true">
        </loading>

        <div class="card w-100">
            <div class="card-body">
                <div class="row">
                    <div class="col-sm-3">
                        <label for="select-area">Khu vực</label>
                        <select
                            name="select-area"
                            id="select-area"
                            class="form-control fpl-custom-height-input"
                            v-model="isFilter.area_id"
                        >
                            <option :value="null">Chọn khu vực</option>
                            <option v-for="area in isFilter.listArea" :value="area.area_id" :key="area.area_id">
                                {{ area.area_name }}
                            </option>
                        </select>
                    </div>

                    <div class="col-sm-3">
                        <label for="select-area">
                            <i class="text-danger">* Lọ<PERSON> phòng theo khu</i>
                        </label>
                        <multiselect
                            v-model="isFilter.room_id"
                            tag-placeholder="Lựa chọn phòng"
                            placeholder="Tìm kiếm hoặc lựa chọn phòng"
                            label="room_name"
                            track-by="id"
                            :allow-empty="true"
                            :options="isFilter.listRoom"
                            :multiple="true"
                            :taggable="true"
                            @tag="addTagRoom"
                            :disabled="isFilter.listRoom.length == 0"
                            class="w-100 fpl-custom-height-input"
                        />
                    </div>
                    <div class="col-sm-3">
                        <label for="input-start-date">Ngày bắt đầu</label>
                        <input
                            class="form-control fpl-custom-height-input"
                            type="date"
                            name="input-start-date"
                            id="input-start-date"
                            :max="isFilter.to_date"
                            v-model="isFilter.from_date"
                        />
                    </div>

                    <div class="col-sm-3">
                        <label for="input-end-date">Ngày kết thúc</label>
                        <input
                            class="form-control fpl-custom-height-input"
                            type="date"
                            name="input-end-date"
                            id="input-end-date"
                            :min="isFilter.from_date"
                            v-model="isFilter.to_date"
                        />
                    </div>
                </div>

                <div class="row mt-10">
                    <div class="col-sm-3">
                        <label for="input-start-date">
                            <i class="text-danger">* Lọc theo ngày bắt đầu và ngày kết thúc</i>
                        </label>
                        <multiselect
                            v-model="isFilter.selectedDateInWeek"
                            tag-placeholder="Lựa chọn thứ"
                            placeholder="Tìm kiếm hoặc lựa chọn thứ"
                            label="text"
                            track-by="value"
                            :allow-empty="true"
                            :options="isFilter.listDateInWeek"
                            :multiple="true"
                            :taggable="true"
                            @tag="addTagDateInWeek"
                            class="w-100 fpl-custom-height-input"
                        />
                    </div>

                    <div class="col-sm-3 zone-btn-check-room d-flex align-items-end">
                        <button class="btn btn-info w-100" @click="handleCheckSchedule()" style="height: 40px;">Tìm kiếm</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="card w-100 mt-10">
            <div class="card-body">
                <div class="zone-table">
                    <table class="table">
                        <thead>
                            <tr class="table-primary">
                                <th :colspan="2" class="text-center">Ngày/Phòng</th>
                                <template v-for="slot in listSlot.length">
                                    <th class="text-center">Ca {{ listSlot[slot - 1] }}</th>
                                </template>
                            </tr>
                        </thead>
                        <tbody>
                            <template v-for="date in listSchedule.length">
                                <template v-for="(room, idx) in listSchedule[date - 1].rooms">
                                    <tr>
                                        <td>
                                            <div class="show-date">{{ listSchedule[date - 1].date }}</div>
                                        </td>
                                        <td>
                                            <div class="room-name">{{ room.room_name }}</div>
                                        </td>
                                        <template v-for="(slot, idx) in room.slots">
                                            <td>
                                                <div
                                                    :class="generateColorStatusBooked(slot.is_booked)"
                                                    @click="showBooked(room.room_id, room.room_name, slot, listSchedule[date - 1].date)"
                                                >
                                                    <span v-if="slot.is_booked">
                                                        <span v-if="slot.group_id" style="font-size: 13px; font-weight: normal;">
                                                            {{ slot.description }} <br>
                                                            {{ slot.group_name }} <br>
                                                            {{ slot.lecturer }} <br>
                                                            {{ slot.subject_code }}
                                                        </span>
                                                        <span v-else>
                                                            No group <br>
                                                            {{ slot.description }}
                                                        </span>
                                                    </span>

                                                    <span v-else>
                                                        <!-- {{ room.room_name }} -->
                                                        Đặt lịch
                                                    </span>
                                                </div>
                                            </td>
                                        </template>
                                    </tr>
                                </template>
                            </template>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <b-modal v-model="showModal" header-close-content hide-footer title="Thông tin phòng">
        <div class="text-center zone-book-room">
                <span>{{ getTextDay(isHandle.infoDate.day) }}, ngày {{ format2Digt(isHandle.infoDate.date) }}, tháng {{ format2Digt(isHandle.infoDate.month) }}, năm {{ isHandle.infoDate.year }} ca {{ isHandle.data.slot_name }} phòng {{ isHandle.room_name }}</span>
            </div>
            <div v-if="!isHandle.data.is_booked">
                <h5 class="text-center font-weight-bold text-success">Phòng đang trống có thể xếp lớp</h5>
            </div>
            <div v-else>
                <div>
                    <div>
                        <h5 class="text-center font-weight-bold text-danger">Phòng đã được xếp lịch</h5>
                    </div>
                    <div>
                        <span><b>Lớp: </b> <a target=”_blank” :href="'/admin/group/edit/'+isHandle.data.group_id"> {{ isHandle.data.group_name }} </a> </span>
                    </div>
                    <div>
                        <span><b>Môn: </b>{{ isHandle.data.subject ? isHandle.data.subject : '' }}</span>
                    </div>
                    <div>
                        <span><b>Giảng viên: </b>{{ isHandle.data.lecturer ? isHandle.data.lecturer : '' }}</span>
                    </div>
                    <div>
                        <span><b>Ghi chú: </b>{{ isHandle.data.description ? isHandle.data.description : '' }}</span>
                    </div>
                </div>
            </div>

            <div class="zone-book-room-form">
                <label for="input-desc">Mô tả</label>
                <textarea
                    v-model="isBookRoom.description"
                    rows="4"
                    class="form-control w-100"
                    id="input-desc"
                    placeholder="Mô tả"
                    :disabled="isHandle.data.group_id"
                />
            </div>

            <div class="zone-book-room-control">
                <div class="handle-create" v-if="isHandle.data.is_booked === false && checkPermissionArr.register == true">
                    <button class="btn btn-success w-100" @click="handleRegisterRoom()">Đặt phòng</button>
                </div>

                <div class="handle-edit-delete" v-if="isHandle.data.is_booked">
                    <div class="row" v-if="!isHandle.data.group_id && checkPermissionArr.delete == true">
                        <div class="col-sm-6 text-center">
                            <button class="btn btn-success" @click="handleUpdateRoom()">Chỉnh sửa</button>
                        </div>

                        <div class="col-sm-6 text-center">
                            <button class="btn btn-danger" @click="handleDeleteBookingRoom()">Xóa lịch</button>
                        </div>
                    </div>
                </div>
            </div>
        </b-modal>
    </div>
</template>

<script>
import Loading from "vue-loading-overlay";
import "vue-loading-overlay/dist/vue-loading.css";

import Multiselect from "vue-multiselect";
import "vue-multiselect/dist/vue-multiselect.min.css";

export default {
    name: 'CheckRoom',
    components: {
        Loading,
        Multiselect,
    },
    data() {
        return {
            checkPermissionArr: {
                register: true,
                update: true,
                delete: true
            },
            listSlot: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
            isLoading: false,
            listAllRoom: [],
            isFilter: {
                area_id: null,
                from_date: '',
                to_date: '',
                selectedDateInWeek: [],
                room_id: [],

                listArea: [],
                listRoom: [],
                listDateInWeek: [
                    {
                        value: 1,
                        text: 'Thứ 2'
                    },
                    {
                        value: 2,
                        text: 'Thứ 3'
                    },
                    {
                        value: 3,
                        text: 'Thứ 4'
                    },
                    {
                        value: 4,
                        text: 'Thứ 5'
                    },
                    {
                        value: 5,
                        text: 'Thứ 6'
                    },
                    {
                        value: 6,
                        text: 'Thứ 7'
                    },
                    {
                        value: 0,
                        text: 'Chủ nhật'
                    },
                ]
            },
            listSchedule: [],
            showModal: false,
            isHandle: {
                room_name: '',
                data: {
                    slot_id: '',
                    slot_name: '',
                    is_booked: '',
                    group_name: '',
                    group_id: '',
                    subject: '',
                    activity_id: '',
                    lecturer: '',
                    description: ''
                },
                date: '',
                infoDate: {
                    year: '',
                    month: '',
                    date: '',
                    day: '',
                }
            },
            isBookRoom: {
                description: '',
            }
        }
    },
    created() {
        this.initData();
    },
    computed: {
        isFilterArea() {
            return this.isFilter.area_id;
        }
    },
    watch: {
        isFilterArea() {
            this.isFilter.room_id = [];
            if (this.isFilter.area_id) {
                this.isFilter.listRoom = this.listAllRoom.filter((room) => room.area_id === this.isFilter.area_id);
            } else {
                this.isFilter.listRoom = [];
            }
        }
    },
    methods: {
        initData() {
            // this.checkPermission('activity_add', 1);
            // this.checkPermission('activity_change', 2);
            // this.checkPermission('activity_delete', 3);
            this.handleGetListRoom();
            this.handleCheckSchedule();
        },

        checkPermission(nameRole, type) {
            let res = false;
            axios.post('/api/v1/role/check-permission', { 
                role_name: nameRole
            }).then((res) => {
                if (type == 1) {
                    this.checkPermissionArr.register = res.data;
                } else if (type == 2) {
                    this.checkPermissionArr.update = res.data;
                } else if (type == 3) {
                    this.checkPermissionArr.delete = res.data;
                }
            }).catch((err) => {
                res =  false;
            });
        },
        handleGetListArea(list) {
            const result = [];
            const len = list.length;
            let idx = 0;

            while (idx < len) {
                const item = list[idx];

                let isExist = false;

                for (let area = 0; area < result.length; area++) {
                    if (result[area].area_id === item.area_id) {
                        isExist = true;
                    }
                }

                if (!isExist) {
                    result.push({
                        area_id: item.area_id,
                        area_name: item.area_name,
                    });
                }

                idx++;
            }

            return result;
        },
        async handleGetListRoom() {
            const PARAMS = {
                all: 1,
                is_offline_area: 1,
            }

            await axios.get('/api/v1/other_manage/getListRoom', { params: PARAMS })
                .then((res) => {
                    this.listAllRoom = res.data;
                    this.isFilter.listArea = this.handleGetListArea(this.listAllRoom);
                    this.isLoading = false;

                })
                .catch((err) => {
                    this.isLoading = false;
                    console.log(err);
                })
        },
        addTagRoom(newRoom) {
            this.isFilter.room_id.push(newRoom);
        },
        addTagDateInWeek(newDate) {
            this.isFilter.selectedDateInWeek.push(newDate);
        },
        handleCheckSchedule() {
            this.isLoading = true;

            const PARAMS = {
                selected_date: this.generateListDateInWeek(),
            };

            // Gửi area_id nếu đã chọn khu vực
            if (this.isFilter.area_id) {
                PARAMS.area_id = this.isFilter.area_id;
            }

            // Gửi room_ids nếu đã chọn phòng cụ thể
            if (this.isFilter.room_id && this.isFilter.room_id.length > 0) {
                PARAMS.room_ids = this.getListIdInList('id', this.isFilter.room_id);
            }

            if (this.isFilter.from_date && this.isFilter.to_date) {
                PARAMS.from_date = this.isFilter.from_date;
                PARAMS.to_date = this.isFilter.to_date;
            }

            if (this.isFilter.from_date && this.isFilter.to_date && PARAMS.selected_date.length === 0) {
                this.listSchedule = [];

                this.isLoading = false;
            } else {
                axios.get('/api/v1/check-schedule/getCheckRoomByDate', { params: PARAMS })
                    .then((res) => {
                        this.listSchedule = res;

                        this.isLoading = false;
                    })
                    .catch((err) => {
                        console.log('Error loading schedule:', err);
                        this.isLoading = false;
                    })
            }
        },
        generateListDateInWeek() {
            let result = [];

            if (!this.isFilter.from_date || !this.isFilter.to_date) {
                return [this.getDateNow()];
            } else {
                let tmpDate = new Date(this.isFilter.from_date);
                let toDate = new Date(this.isFilter.to_date);

                if (tmpDate.getTime() === toDate.getTime()) {
                    const SELECTED_DATE = this.getListIdInList('value', this.isFilter.selectedDateInWeek);

                    if (SELECTED_DATE.includes(tmpDate.getDay())) {
                        return [this.isFilter.to_date];
                    }

                    if (SELECTED_DATE.length === 0) {
                        result = [];

                        while (tmpDate.getTime() <= toDate.getTime()) {
                            result.push(`${tmpDate.getFullYear()}-${this.format2Digt(tmpDate.getMonth() + 1)}-${this.format2Digt(tmpDate.getDate())}`);

                            tmpDate = new Date(tmpDate.getTime() + (24 * 60 * 60 * 1000));
                        }

                        return result;
                    }

                    return [];
                }

                let LIST_DATE_IN_WEEK = [];

                if (this.isFilter.selectedDateInWeek.length) {
                    LIST_DATE_IN_WEEK = this.getListIdInList('value', this.isFilter.selectedDateInWeek);
                } else {
                    LIST_DATE_IN_WEEK = [0, 1, 2, 3, 4, 5, 6];
                }

                while (tmpDate.getTime() <= toDate.getTime()) {
                    if (LIST_DATE_IN_WEEK.includes(tmpDate.getDay())) {
                        result.push(`${tmpDate.getFullYear()}-${this.format2Digt(tmpDate.getMonth() + 1)}-${this.format2Digt(tmpDate.getDate())}`);
                    }

                    tmpDate = new Date(tmpDate.getTime() + (24 * 60 * 60 * 1000));
                }
            }

            return result;
        },
        getDateNow() {
            const d = new Date();

            return `${d.getFullYear()}-${this.format2Digt(d.getMonth() + 1)}-${this.format2Digt(d.getDate())}`;
        },
        generateColorStatusBooked(status) {
            return status ? 'show-room-name has-book book-border' : 'show-room-name has-no-book book-white book-border';
        },
        showBooked(room_id, room_name, data, date) {
            this.isHandle = {
                room_id,
                room_name,
                data,
                date,
                infoDate: this.getInfoDate(date),
            };

            this.isBookRoom.description = this.isHandle.data.description;

            if (this.isHandle.data.is_booked && this.isHandle.data.group_id) {
                this.showModal = true;
                // window.location.href = `/admin/group/edit/${this.isHandle.data.group_id}`;
            } else {
                this.showModal = true;
            }
        },
        getListIdInList(key, list) {
            const len = list.length;
            let idx = 0;

            const result = [];

            while (idx < len) {
                result.push(list[idx][key]);

                idx++;
            }

            return result;
        },
        getInfoDate(date) {
            const d = new Date(date);

            return {
                year: d.getFullYear(),
                month: d.getMonth() + 1,
                date: d.getDate(),
                day: d.getDay(),
            }
        },
        getTextDay(day) {
            const LIBRARY = {
                0: 'Chủ nhật',
                1: 'Thứ 2',
                2: 'Thứ 3',
                3: 'Thứ 4',
                4: 'Thứ 5',
                5: 'Thứ 6',
                6: 'Thứ 7',
            };

            return LIBRARY[day];
        },
        format2Digt(num) {
            return num < 10 ? '0' + num : num;
        },
        handleRegisterRoom() {
            const ROOM = this.listAllRoom.find(room => room.id === this.isHandle.room_id);

            const BODY = {
                area_id: ROOM.area_id,
                room_id: this.isHandle.room_id,
                date: this.isHandle.date,
                slot: this.isHandle.data.slot_id,
                description: this.isBookRoom.description
            };

            this.isLoading = true;

            axios.post('/api/v1/check-schedule/postRegisterRoom', BODY)
                .then(() => {
                    this.showModal = false;
                    this.handleCheckSchedule();

                    this.isLoading = false;
                })
                .catch((err) => {
                    console.log(err);
                    this.isLoading = false;
                })
        },
        handleUpdateRoom() {
            const BODY = {
                activity_id: this.isHandle.data.activity_id,
                description: this.isBookRoom.description,
            };

            this.isLoading = true;
            axios.put('/api/v1/check-schedule/putUpdateBookingRoom', BODY)
                .then(() => {
                    this.showModal = false;
                    this.handleCheckSchedule();

                    this.isLoading = false;
                })
                .catch((err) => {
                    console.log(err);
                    this.isLoading = false;
                })
        },
        handleDeleteBookingRoom() {
            this.isLoading = true;
            axios.delete(`/api/v1/check-schedule/deleteBookingRoom?activity_id=${this.isHandle.data.activity_id}`)
                .then(() => {
                    this.showModal = false;
                    this.handleCheckSchedule();

                    this.isLoading = false;
                })
                .catch((err) => {
                    console.log(err);
                    this.isLoading = false;
                })
        }
    },
}
</script>

<style lang="scss" scoped>
    .fpl-custom-height-input {
        height: 40px;
    }

    .zone-btn-check-room {
        margin-top: 10px;
    }

    .mt-10 {
        margin-top: 10px;
    }

    .zone-table {
        overflow: auto;

        height: calc(100vh - 220px);

        table {
            thead {
                tr {
                    position: sticky;
                    top: 0;
                }
            }

            tbody {
                tr {
                    td {
                        min-width: 130px;
                        vertical-align: middle;
                    }
                }
            }
        }
    }

    .show-date {
        padding: 5px;
        vertical-align: middle;

        text-align: center;
        font-weight: 600;
    }

    .room-name {
        padding: 5px;
        font-weight: 600;

        border-radius: 5px;
        text-align: center;
    }

    .show-room-name {
        padding: 5px;
        font-weight: 600;

        border-radius: 5px;
        min-width: 170px;

        &:not(:last-child) {
            margin-bottom: 5px;
        }
    }

    .has-book {
        color: #0B5ED7;
        background: #fff;

        text-align: center;

        cursor: pointer;
    }

    .has-no-book {
        background-color: #28a745;
        color: #ffffff;

        text-align: center;

        cursor: pointer;
    }

    .zone-book-room {
        margin: 10px 0;

    }

    .zone-book-room-form {
        margin: 10px 0;
    }

    .book-white{
        background-color: #ffffff;
        color: #666;
        text-align: center;
        cursor: pointer;
        border: 1px solid #ccc;
    }

    .book-border{
        border: 1px solid #9191916e;
    }
</style>
